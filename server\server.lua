local QBCore = exports['qb-core']:GetCoreObject()

local cuffedPlayers = {}
local taseredPlayers = {}

AddEventHandler('playerDropped', function(reason)
    local playerId = source
    if cuffedPlayers[playerId] then
        cuffedPlayers[playerId] = nil
    end
    if taseredPlayers[playerId] then
        taseredPlayers[playerId] = nil
    end
end)

RegisterServerEvent('vanguard_policejob:attemptTackle')
AddEventHandler('vanguard_policejob:attemptTackle', function(targetId)
    TriggerClientEvent('vanguard_policejob:tackled', targetId, source)
    TriggerClientEvent('vanguard_policejob:tackle', source)
end)

RegisterServerEvent('vanguard_policejob:escortPlayer')
AddEventHandler('vanguard_policejob:escortPlayer', function(targetId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local hasJob
    for i=1, #Config.policeJobs do
        if Player.PlayerData.job.name == Config.policeJobs[i] then
            hasJob = Player.PlayerData.job.name
            break
        end
    end
    if hasJob then
        TriggerClientEvent('vanguard_policejob:setEscort', src, targetId)
        TriggerClientEvent('vanguard_policejob:escortedPlayer', targetId, src)
    end
end)

RegisterServerEvent('vanguard_policejob:inVehiclePlayer')
AddEventHandler('vanguard_policejob:inVehiclePlayer', function(targetId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if Player.PlayerData.job.name == "police" then
        TriggerClientEvent('vanguard_policejob:stopEscorting', src)
        TriggerClientEvent('vanguard_policejob:putInVehicle', targetId)
    end
end)

RegisterServerEvent('vanguard_policejob:outVehiclePlayer')
AddEventHandler('vanguard_policejob:outVehiclePlayer', function(targetId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    if Player.PlayerData.job.name == "police" then
        TriggerClientEvent('vanguard_policejob:takeFromVehicle', targetId)
    end
end)

RegisterServerEvent('vanguard_policejob:setCuff')
AddEventHandler('vanguard_policejob:setCuff', function(isCuffed)
    cuffedPlayers[source] = isCuffed
end)

RegisterServerEvent('vanguard_policejob:playerTasered')
AddEventHandler('vanguard_policejob:playerTasered', function(targetId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    if Player and Player.PlayerData.job.name == "police" then
        taseredPlayers[targetId] = os.time()
        TriggerClientEvent('vanguard_policejob:onTasered', targetId, src)
    end
end)

RegisterServerEvent('vanguard_policejob:handcuffPlayer')
AddEventHandler('vanguard_policejob:handcuffPlayer', function(target)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    if Player.PlayerData.job.name == "police" then
        if cuffedPlayers[target] then
            TriggerClientEvent('vanguard_policejob:uncuffAnim', src, target)
            Wait(4000)
            TriggerClientEvent('vanguard_policejob:uncuff', target)
        else
            if Config.handcuff.requireItem then
                local handcuffs = Player.Functions.GetItemByName(Config.handcuff.item)
                if handcuffs and handcuffs.amount > 0 then
                    TriggerClientEvent('vanguard_policejob:arrested', target, src)
                    TriggerClientEvent('vanguard_policejob:arrest', src)
                else
                    TriggerClientEvent('QBCore:Notify', src, Strings.need_handcuffs, 'error')
                end
            else
                TriggerClientEvent('vanguard_policejob:arrested', target, src)
                TriggerClientEvent('vanguard_policejob:arrest', src)
            end
        end
    end
end)

local function getPoliceOnline()
    local players = QBCore.Functions.GetPlayers()
    local count = 0
    for i = 1, #players do
        local Player = QBCore.Functions.GetPlayer(players[i])
        if Player.PlayerData.job.name == "police" and Player.PlayerData.job.onduty then
            count = count + 1
        end
    end
    return count
end

exports('getPoliceOnline', getPoliceOnline)

QBCore.Functions.CreateCallback('vanguard_policejob:getJobLabel', function(source, cb, job)
    cb(QBCore.Shared.Jobs[job].label or Strings.police)
end)

QBCore.Functions.CreateCallback('vanguard_policejob:isCuffed', function(source, cb, target)
    cb(cuffedPlayers[target] or false)
end)

QBCore.Functions.CreateCallback('vanguard_policejob:isRecentlyTasered', function(source, cb, target)
    if taseredPlayers[target] then
        local currentTime = os.time()
        local taserTime = taseredPlayers[target]
        local timeDiff = (currentTime - taserTime) * 1000

        if timeDiff <= (Config.handcuff.taserWindow or 10000) then
            cb(true)
        else
            taseredPlayers[target] = nil
            cb(false)
        end
    else
        cb(false)
    end
end)



QBCore.Functions.CreateCallback('vanguard_policejob:getVehicleOwner', function(source, cb, plate)
    local result = MySQL.Sync.fetchAll('SELECT citizenid FROM player_vehicles WHERE plate = @plate', {
        ['@plate'] = plate
    })

    if result[1] then
        local citizenid = result[1].citizenid
        local result2 = MySQL.Sync.fetchAll('SELECT charinfo FROM players WHERE citizenid = @citizenid', {
            ['@citizenid'] = citizenid
        })

        if result2[1] then
            local charinfo = json.decode(result2[1].charinfo)
            cb(charinfo.firstname .. ' ' .. charinfo.lastname)
        else
            cb(false)
        end
    else
        cb(false)
    end
end)

QBCore.Functions.CreateCallback('vanguard_policejob:canPurchase', function(source, cb, data)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local itemData

    if data.grade > #Config.Locations[data.id].armoury.weapons then
        itemData = Config.Locations[data.id].armoury.weapons[#Config.Locations[data.id].armoury.weapons][data.itemId]
    elseif not Config.Locations[data.id].armoury.weapons[data.grade] then
        return
    else
        itemData = Config.Locations[data.id].armoury.weapons[data.grade][data.itemId]
    end

    if not itemData.price then
        if string.sub(data.itemId, 1, 7) == 'WEAPON_' and not Config.weaponsAsItems then
            Player.Functions.AddItem(data.itemId, 1)
            TriggerClientEvent('inventory:client:ItemBox', src, QBCore.Shared.Items[data.itemId], 'add')
        else
            Player.Functions.AddItem(data.itemId, data.quantity)
            TriggerClientEvent('inventory:client:ItemBox', src, QBCore.Shared.Items[data.itemId], 'add')
        end
        cb(true)
    else
        local bankBalance = Player.PlayerData.money["bank"]
        if bankBalance < itemData.price then
            cb(false)
        else
            Player.Functions.RemoveMoney('bank', itemData.price, "police-equipment-purchase")
            if string.sub(data.itemId, 1, 7) == 'WEAPON_' and not Config.weaponsAsItems then
                Player.Functions.AddItem(data.itemId, 1)
                TriggerClientEvent('inventory:client:ItemBox', src, QBCore.Shared.Items[data.itemId], 'add')
            else
                Player.Functions.AddItem(data.itemId, data.quantity)
                TriggerClientEvent('inventory:client:ItemBox', src, QBCore.Shared.Items[data.itemId], 'add')
            end
            cb(true)
        end
    end
end)

QBCore.Functions.CreateCallback('vanguard_policejob:checkPlayerId', function(source, cb, targetId)
    local Player = QBCore.Functions.GetPlayer(targetId)
    if not Player then return cb({}) end

    local PlayerData = Player.PlayerData
    local charInfo = PlayerData.charinfo

    local data = {
        name = charInfo.firstname .. ' ' .. charInfo.lastname,
        job = QBCore.Shared.Jobs[PlayerData.job.name].label,
        position = PlayerData.job.grade.name,
        dob = charInfo.birthdate,
        sex = charInfo.gender == 0 and 'Male' or 'Female'
    }

    local licenses = {}
    local success, result = pcall(function()
        return MySQL.Sync.fetchAll('SELECT * FROM player_licenses WHERE citizenid = @citizenid', {
            ['@citizenid'] = PlayerData.citizenid
        })
    end)

    if success and result and #result > 0 then
        for i=1, #result do
            if QBCore.Shared.Licenses and QBCore.Shared.Licenses[result[i].type] then
                table.insert(licenses, {
                    type = result[i].type,
                    label = QBCore.Shared.Licenses[result[i].type].label
                })
            end
        end
    end

    data.licenses = licenses
    cb(data)
end)

RegisterNetEvent('qb-policejob:ToggleDuty', function()
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)

    if not Player then return end
    if Player.PlayerData.job.name ~= "police" then return end

    Player.Functions.SetJobDuty(not Player.PlayerData.job.onduty)
end)

RegisterNetEvent('vanguard_policejob:jailPlayer', function(targetId, jailTime)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    local TargetPlayer = QBCore.Functions.GetPlayer(targetId)

    if not Player or Player.PlayerData.job.name ~= 'police' or not Player.PlayerData.job.onduty then
        return
    end

    if not TargetPlayer then
        TriggerClientEvent('QBCore:Notify', src, 'Player not found', 'error')
        return
    end

    TriggerClientEvent('prison:client:Enter', targetId, jailTime)

    local officerName = Player.PlayerData.charinfo.firstname .. ' ' .. Player.PlayerData.charinfo.lastname
    local prisonerName = TargetPlayer.PlayerData.charinfo.firstname .. ' ' .. TargetPlayer.PlayerData.charinfo.lastname

    TriggerClientEvent('QBCore:Notify', src,
        string.format('You sent %s to prison for %d minutes', prisonerName, jailTime), 'success')
    TriggerClientEvent('QBCore:Notify', targetId,
        string.format('You have been sent to prison for %d minutes by %s', jailTime, officerName), 'error')
end)