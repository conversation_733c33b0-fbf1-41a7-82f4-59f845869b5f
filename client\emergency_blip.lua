local QBCore = exports['qb-core']:GetCoreObject()

if Config.PlayerBlips.ENABLE then

    local PlayerBlips = {}
    local playerLoaded = false
    local playerJob = nil
    local ShowBlips = false  
    local NetCheck = false   

    local function IsJobAllowed(job, allowedJobs)
        for i = 1, #allowedJobs do
            if job == allowedJobs[i] then
                return true
            end
        end
        return false
    end

    local function GetPlayerInfo()
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local heading = GetEntityHeading(ped)

        local vehicle_type = 'foot'
        if IsPedInAnyVehicle(ped, false) then
            local vehicle = GetVehiclePedIsIn(ped, false)
            local vehicleClass = GetVehicleClass(vehicle)

            if vehicleClass == 8 then
                vehicle_type = 'motorcycle'
            elseif vehicleClass == 15 or vehicleClass == 16 then 
                vehicle_type = 'helicopter'
            elseif vehicleClass == 14 then 
                vehicle_type = 'boat'
            else 
                vehicle_type = 'car'
            end
        end

        return {
            coords = coords,
            heading = heading,
            vehicle_type = vehicle_type
        }
    end

    CreateThread(function()
        while true do
            Wait(1000)
            local PlayerData = QBCore.Functions.GetPlayerData()
            if PlayerData and PlayerData.job and PlayerData.job.onduty and
               IsJobAllowed(PlayerData.job.name, Config.PlayerBlips.allowed_jobs) then
                TriggerServerEvent('vanguard_policejob:PlayerBlips:requestData')
            end
        end
    end)



    RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
        playerLoaded = true
        local PlayerData = QBCore.Functions.GetPlayerData()
        playerJob = PlayerData.job.name

        if IsJobAllowed(playerJob, Config.PlayerBlips.allowed_jobs) then

            ShowBlips = true
            NetCheck = true
        end
    end)

    RegisterNetEvent('QBCore:Client:OnJobUpdate', function(JobInfo)
        local oldJob = playerJob
        playerJob = JobInfo.name

        if not IsJobAllowed(playerJob, Config.PlayerBlips.allowed_jobs) or (oldJob ~= playerJob) then
            local myServerId = GetPlayerServerId(PlayerId())
            
            for source, blipData in pairs(PlayerBlips) do
                if blipData.blip and DoesBlipExist(blipData.blip) then
                    if source ~= myServerId then
                        RemoveBlip(blipData.blip)
                    end
                end
            end
            PlayerBlips = {}
            TriggerServerEvent('vanguard_policejob:PlayerBlips:removeMyBlip')
            
            local blip = GetMainPlayerBlipId()
            if blip then
                SetBlipSprite(blip, 6)
                SetBlipDisplay(blip, 4)
                SetBlipScale(blip, 0.7)
                SetBlipFlashes(blip, false)
                ShowHeadingIndicatorOnBlip(blip, false)
                BeginTextCommandSetBlipName('STRING')
                AddTextComponentString(GetPlayerName(PlayerId()))
                EndTextCommandSetBlipName(blip)
                SetBlipCategory(blip, 1)
                SetBlipColour(blip, 0)
            end
        end
        
        if IsJobAllowed(playerJob, Config.PlayerBlips.allowed_jobs) and JobInfo.onduty then

            ShowBlips = true
            NetCheck = true
        else

            ShowBlips = false
            NetCheck = false
        end
    end)

    RegisterNetEvent('QBCore:Client:SetDuty', function(onDuty)
        local PlayerData = QBCore.Functions.GetPlayerData()
        
        if PlayerData and PlayerData.job then
            playerJob = PlayerData.job.name
            
            if not onDuty then
                for source, blipData in pairs(PlayerBlips) do
                    if blipData.blip and DoesBlipExist(blipData.blip) then
                        local myServerId = GetPlayerServerId(PlayerId())
                        if source ~= myServerId then
                            RemoveBlip(blipData.blip)
                        end
                    end
                end
                PlayerBlips = {}
                TriggerServerEvent('vanguard_policejob:PlayerBlips:removeMyBlip')
                
                local blip = GetMainPlayerBlipId()
                if blip then
                    SetBlipSprite(blip, 6)
                    SetBlipDisplay(blip, 4)
                    SetBlipScale(blip, 0.7)
                    SetBlipFlashes(blip, false)
                    ShowHeadingIndicatorOnBlip(blip, false)
                    BeginTextCommandSetBlipName('STRING')
                    AddTextComponentString(GetPlayerName(PlayerId()))
                    EndTextCommandSetBlipName(blip)
                    SetBlipCategory(blip, 1)
                    SetBlipColour(blip, 0)
                end
            elseif IsJobAllowed(playerJob, Config.PlayerBlips.allowed_jobs) then

                ShowBlips = true
                NetCheck = true
                
                Wait(1000)
                local playerInfo = GetPlayerInfo()
                local myData = {
                    coords = playerInfo.coords,
                    heading = playerInfo.heading,
                    name = PlayerData.charinfo.firstname .. " " .. PlayerData.charinfo.lastname,
                    job = playerJob,
                    source = GetPlayerServerId(PlayerId()),
                    vehicle_type = playerInfo.vehicle_type,
                    flashing_blip = false
                }
                TriggerServerEvent('vanguard_policejob:PlayerBlips:updateData', myData)
            end
        end
    end)

        local function CreatePlayerBlip(source, data)
            local myServerId = GetPlayerServerId(PlayerId())
            local blip

            if source == myServerId then
                if not PlayerBlips[source] or not PlayerBlips[source].blip then
                    blip = GetMainPlayerBlipId()
                    while not blip do
                        Wait(0)
                        blip = GetMainPlayerBlipId()
                    end
                else
                    blip = PlayerBlips[source].blip
                end
            else
                if PlayerBlips[source] and PlayerBlips[source].blip then
                    RemoveBlip(PlayerBlips[source].blip)
                end
                -- استخدام AddBlipForCoord بدلاً من AddBlipForEntity لضمان الظهور على أي مسافة
                blip = AddBlipForCoord(data.coords.x, data.coords.y, data.coords.z)
            end

            local sprite = Config.PlayerBlips.blip_sprites['foot']
            if data.vehicle_type then
                sprite = Config.PlayerBlips.blip_sprites[data.vehicle_type] or sprite
            end

            SetBlipSprite(blip, sprite)
            SetBlipDisplay(blip, 4)
            SetBlipScale(blip, 0.9)

            -- إعدادات لضمان الظهور على أي مسافة
            SetBlipAsShortRange(blip, false)  -- عدم تقييد المسافة
            SetBlipAlpha(blip, 255)  -- شفافية كاملة

            local color = Config.PlayerBlips.blip_colours[data.job] and
                         Config.PlayerBlips.blip_colours[data.job].default or 3
            if data.flashing_blip then
                color = Config.PlayerBlips.blip_colours[data.job] and
                       Config.PlayerBlips.blip_colours[data.job].emergency or 1
            end
            SetBlipColour(blip, color)

            SetBlipFlashes(blip, false)
            ShowHeadingIndicatorOnBlip(blip, true)
            BeginTextCommandSetBlipName('STRING')
            AddTextComponentString(data.name or "Police Officer")
            EndTextCommandSetBlipName(blip)
            SetBlipCategory(blip, 2)
            SetBlipShrink(blip, false)  -- عدم تصغير البلب على المسافات الطويلة

            if data.flashing_blip and Config.PlayerBlips.flashing_blips then
                TriggerEvent('vanguard_policejob:PlayerBlips_flash', {blip = blip, data = data, blip_colour = {color, Config.PlayerBlips.blip_colours[data.job] and Config.PlayerBlips.blip_colours[data.job].emergency or 1}, name = data.name})
            end

            PlayerBlips[source] = {
                blip = blip,
                data = data,
                flashing_blip = data.flashing_blip
            }
        end

        RegisterNetEvent('vanguard_policejob:PlayerBlips:Show', function(players)
            local PlayerData = QBCore.Functions.GetPlayerData()
            if not PlayerData or not PlayerData.job or not PlayerData.job.onduty or
               not IsJobAllowed(PlayerData.job.name, Config.PlayerBlips.allowed_jobs) then
                return
            end

            for _, player in pairs(players) do
                local playeridx = GetPlayerFromServerId(player.source)
                local myServerId = GetPlayerServerId(PlayerId())

                if playeridx > 0 then 
                    local ped = GetPlayerPed(playeridx)
                    local blip

                    if player.source == myServerId then
                        blip = GetMainPlayerBlipId()
                        if not blip then
                            local attempts = 0
                            while not blip and attempts < 10 do
                                Wait(100)
                                blip = GetMainPlayerBlipId()
                                attempts = attempts + 1
                            end
                        end
                    else
                        -- استخدام AddBlipForCoord بدلاً من AddBlipForEntity لضمان الظهور على أي مسافة
                        local existingBlip = PlayerBlips[player.source] and PlayerBlips[player.source].blip
                        if existingBlip and DoesBlipExist(existingBlip) then
                            RemoveBlip(existingBlip)
                        end
                        blip = AddBlipForCoord(player.coords.x, player.coords.y, player.coords.z)
                    end

                    if blip then

                        local sprite = Config.PlayerBlips.blip_sprites['foot']
                        local ped = GetPlayerPed(playeridx)
                        if ped and IsPedInAnyVehicle(ped, false) then
                            local vehicle = GetVehiclePedIsIn(ped, false)
                            local vehicleClass = GetVehicleClass(vehicle)

                            if vehicleClass == 8 then
                                sprite = Config.PlayerBlips.blip_sprites['motorcycle']
                            elseif vehicleClass == 15 or vehicleClass == 16 then
                                sprite = Config.PlayerBlips.blip_sprites['helicopter']
                            elseif vehicleClass == 14 then
                                sprite = Config.PlayerBlips.blip_sprites['boat']
                            else
                                sprite = Config.PlayerBlips.blip_sprites['car']
                            end
                        end

                        SetBlipSprite(blip, sprite)
                        SetBlipDisplay(blip, 4)
                        SetBlipScale(blip, 0.85)

                        -- إعدادات لضمان الظهور على أي مسافة
                        SetBlipAsShortRange(blip, false)  -- عدم تقييد المسافة
                        SetBlipAlpha(blip, 255)  -- شفافية كاملة

                        if player.source ~= myServerId then
                            ShowHeadingIndicatorOnBlip(blip, true)
                            -- تحديث الاتجاه بناءً على الإحداثيات المحفوظة
                            local ped = GetPlayerPed(playeridx)
                            if ped and DoesEntityExist(ped) then
                                SetBlipRotation(blip, math.ceil(GetEntityHeading(ped)))
                            end
                        end

                        local color = Config.PlayerBlips.blip_colours[player.job] and
                                     Config.PlayerBlips.blip_colours[player.job].default or 3
                        SetBlipColour(blip, color)

                        SetBlipFlashes(blip, false)
                        BeginTextCommandSetBlipName('STRING')
                        AddTextComponentString(player.name or "Police Officer")
                        EndTextCommandSetBlipName(blip)
                        SetBlipCategory(blip, 2)
                        SetBlipShrink(blip, false)  -- عدم تصغير البلب على المسافات الطويلة

                        PlayerBlips[player.source] = {
                            blip = blip,
                            data = player
                        }
                    end
                end
            end
        end)

        RegisterNetEvent('vanguard_policejob:PlayerBlips_remove')
        AddEventHandler('vanguard_policejob:PlayerBlips_remove', function(source)
            local myServerId = GetPlayerServerId(PlayerId())
            if PlayerBlips[source] and PlayerBlips[source].blip then
                if source == myServerId then
                    local blip = GetMainPlayerBlipId()
                    SetBlipSprite(blip, 6)
                    SetBlipDisplay(blip, 4)
                    SetBlipScale(blip, 0.7)
                    SetBlipFlashes(blip, false)
                    ShowHeadingIndicatorOnBlip(blip, false)
                    BeginTextCommandSetBlipName('STRING')
                    AddTextComponentString(GetPlayerName(PlayerId()))
                    EndTextCommandSetBlipName(blip)
                    SetBlipCategory(blip, 1)
                else
                    RemoveBlip(PlayerBlips[source].blip)
                end
                PlayerBlips[source] = nil
            end
        end)

        if Config.PlayerBlips.flashing_blips then
            RegisterNetEvent('vanguard_policejob:PlayerBlips_flash')
            AddEventHandler('vanguard_policejob:PlayerBlips_flash', function(data)
                if not data.blip_colour or not data.blip_colour[2] then return end
                Wait(Config.PlayerBlips.update_timer * 1000 / 2)
                SetBlipColour(data.blip, data.blip_colour[2])
                BeginTextCommandSetBlipName('STRING')
                AddTextComponentString(data.name)
                EndTextCommandSetBlipName(data.blip)
            end)
        end

        if Config.PlayerBlips.flashing_blips then
            CreateThread(function()
                while true do
                    Wait(1000)

                    local PlayerData = QBCore.Functions.GetPlayerData()
                    if PlayerData and PlayerData.job and PlayerData.job.onduty and
                       IsJobAllowed(playerJob, Config.PlayerBlips.allowed_jobs) then
                        local ped = PlayerPedId()
                        local vehicle = GetVehiclePedIsIn(ped, false)
                        local myServerId = GetPlayerServerId(PlayerId())

                        if vehicle ~= 0 then
                            local isEmergencyLightsOn = IsVehicleSirenOn(vehicle)
                            local currentStatus = PlayerBlips[myServerId] and
                            PlayerBlips[myServerId].data and
                            PlayerBlips[myServerId].data.flashing_blip

                            if isEmergencyLightsOn and not currentStatus then
                                TriggerServerEvent('vanguard_policejob:PlayerBlips:emergancylights', true)
                            elseif not isEmergencyLightsOn and currentStatus then
                                TriggerServerEvent('vanguard_policejob:PlayerBlips:emergancylights', false)
                            end
                        elseif PlayerBlips[myServerId] and
                               PlayerBlips[myServerId].data and
                               PlayerBlips[myServerId].data.flashing_blip then
                            TriggerServerEvent('vanguard_policejob:PlayerBlips:emergancylights', false)
                        end
                    end
                end
            end)
        end

    local function ResetMainBlip()
        local blip = GetMainPlayerBlipId()
        if blip then
            SetBlipSprite(blip, 6)
            SetBlipDisplay(blip, 4)
            SetBlipScale(blip, 0.7)
            SetBlipFlashes(blip, false)
            ShowHeadingIndicatorOnBlip(blip, false)
            BeginTextCommandSetBlipName('STRING')
            AddTextComponentString(GetPlayerName(PlayerId()))
            EndTextCommandSetBlipName(blip)
            SetBlipCategory(blip, 1)
        end
    end

    AddEventHandler('onResourceStop', function(resource)
        if resource == GetCurrentResourceName() then
            ResetMainBlip()
        end
    end)

    AddEventHandler('QBCore:Client:OnPlayerUnload', function()
        for source, blipData in pairs(PlayerBlips) do
            if blipData.blip and DoesBlipExist(blipData.blip) then
                local myServerId = GetPlayerServerId(PlayerId())
                if source ~= myServerId then
                    RemoveBlip(blipData.blip)
                end
            end
        end
        PlayerBlips = {}
        TriggerServerEvent('vanguard_policejob:PlayerBlips:removeMyBlip')
        ResetMainBlip()
    end)
end
