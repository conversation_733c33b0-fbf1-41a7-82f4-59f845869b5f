local QBCore = exports['qb-core']:GetCoreObject()

if Config.PlayerBlips.ENABLE then

    local PlayerBlips = {}
    local EmergencyBlips = {}

    local function IsJobAllowed(job, allowedJobs)
        for i = 1, #allowedJobs do
            if job == allowedJobs[i] then
                return true
            end
        end
        return false
    end

    local function GetPlayersForBlips()
        local playerReturn = {}
        local players = QBCore.Functions.GetPlayers()

        for i = 1, #players do
            local Player = QBCore.Functions.GetPlayer(players[i])
            if Player and Player.PlayerData.job.onduty and
               IsJobAllowed(Player.PlayerData.job.name, Config.PlayerBlips.allowed_jobs) then

                local playerPed = GetPlayerPed(players[i])
                local name = (Player.PlayerData.charinfo.firstname or '') .. ' ' .. (Player.PlayerData.charinfo.lastname or '')

                playerReturn[#playerReturn + 1] = {
                    name = name,
                    source = players[i],
                    coords = GetEntityCoords(playerPed),
                    heading = GetEntityHeading(playerPed),  -- إضافة الاتجاه
                    job = Player.PlayerData.job.name,
                    citizenid = Player.PlayerData.citizenid
                }
            end
        end
        return playerReturn
    end

    local function GetPlayerInfo(source)
        local Player = QBCore.Functions.GetPlayer(source)
        if not Player then return nil end

        local ped = GetPlayerPed(source)
        local coords = GetEntityCoords(ped)
        local heading = GetEntityHeading(ped)

        local vehicle_type = 'foot' 

        return {
            coords = coords,
            heading = heading,
            char_name = Player.PlayerData.charinfo.firstname .. " " .. Player.PlayerData.charinfo.lastname,
            job = Player.PlayerData.job.name,
            source = source,
            vehicle_type = vehicle_type
        }
    end

        RegisterServerEvent('vanguard_policejob:PlayerBlips:emergancylights')
        AddEventHandler('vanguard_policejob:PlayerBlips:emergancylights', function(status)
            local src = source
            local Player = QBCore.Functions.GetPlayer(src)

            if Player and IsJobAllowed(Player.PlayerData.job.name, Config.PlayerBlips.allowed_jobs) then
                PlayerBlips[src] = PlayerBlips[src] or {}
                PlayerBlips[src].flashing_blip = status
                PlayerBlips[src].source = src
                PlayerBlips[src].job = Player.PlayerData.job.name

                TriggerClientEvent('vanguard_policejob:PlayerBlips_update', -1, src, PlayerBlips[src])
            end
        end)

        RegisterServerEvent('vanguard_policejob:PlayerBlips:updateData')
        AddEventHandler('vanguard_policejob:PlayerBlips:updateData', function(data)
            local src = source
            local Player = QBCore.Functions.GetPlayer(src)

            if Player and IsJobAllowed(Player.PlayerData.job.name, Config.PlayerBlips.allowed_jobs) then
                PlayerBlips[src] = data
                PlayerBlips[src].source = src
                PlayerBlips[src].job = Player.PlayerData.job.name

                TriggerClientEvent('vanguard_policejob:PlayerBlips_update', -1, src, PlayerBlips[src])
            end
        end)

        AddEventHandler('playerDropped', function(reason)
            local src = source
            if PlayerBlips[src] then
                TriggerClientEvent('vanguard_policejob:PlayerBlips_remove', -1, src)
                PlayerBlips[src] = nil
            end
        end)

        RegisterServerEvent('vanguard_policejob:PlayerBlips:requestData')
        AddEventHandler('vanguard_policejob:PlayerBlips:requestData', function()
            local src = source
            local Player = QBCore.Functions.GetPlayer(src)

            if Player and IsJobAllowed(Player.PlayerData.job.name, Config.PlayerBlips.allowed_jobs) then
                local players = GetPlayersForBlips()
                TriggerClientEvent('vanguard_policejob:PlayerBlips:Show', src, players)
            else
            end
        end)

        RegisterServerEvent('vanguard_policejob:PlayerBlips:removeMyBlip')
        AddEventHandler('vanguard_policejob:PlayerBlips:removeMyBlip', function()
            local src = source
            if PlayerBlips[src] then
                TriggerClientEvent('vanguard_policejob:PlayerBlips_remove', -1, src)
                PlayerBlips[src] = nil
            end
        end)

        RegisterNetEvent('QBCore:Server:SetDuty', function(src, onDuty)
            if not onDuty and PlayerBlips[src] then
                TriggerClientEvent('vanguard_policejob:PlayerBlips_remove', -1, src)
                PlayerBlips[src] = nil
            end
        end)
    end
