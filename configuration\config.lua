--  ██████╗ ███████╗██╗   ██╗    ██╗     █████╗ ██╗  ██╗███╗   ███╗███████╗██████╗ ████████╗██╗ ██████╗ ███████╗██████╗
--  ██╔══██╗██╔════╝██║   ██║    ██║    ██╔══██╗██║  ██║████╗ ████║██╔════╝██╔══██╗╚══██╔══╝██║██╔════╝ ██╔════╝██╔══██╗
--  ██║  ██║█████╗  ██║   ██║    ██║    ███████║███████║██╔████╔██║█████╗  ██║  ██║   ██║   ██║██║  ███╗█████╗  ██████╔╝
--  ██║  ██║██╔══╝  ╚██╗ ██╔╝    ██║    ██╔══██║██╔══██║██║╚██╔╝██║██╔══╝  ██║  ██║   ██║   ██║██║   ██║██╔══╝  ██╔══██╗
--  ██████╔╝███████╗ ╚████╔╝     ██║    ██║  ██║██║  ██║██║ ╚═╝ ██║███████╗██████╔╝   ██║   ██║╚██████╔╝███████╗██║  ██║
--  ╚═════╝ ╚══════╝  ╚═══╝      ╚═╝    ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝     ╚═╝╚══════╝╚═════╝    ╚═╝   ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝

local seconds, minutes = 1000, 60000

Config = {}

Config.Framework = 'Legacy'



--------------------------------------------------------------------------------------------------------
-------------------------- DUTY CONFIG  ----------------------------------------------------------------
--------------------------------------------------------------------------------------------------------

Config.Jobs = {
    Police = {
        ClockedInJob = "police",
        ClockedOutJob = "offpolice",
        Locations = {
            vector3(90.53, -417.36, 48.32)
        },
        Webhook = 'https://discord.com/api/webhooks/1373325300947095705/G1f7bmU66zxbFlp5GmrBm4yDAZXWMWT_rIcdB0OiK49iRDGLR5lwWW6fIpmGn69IkhEL',
        Avatar = 'https://i.imgur.com/BuKIMkc.png',
        EnableWelcomeMessage = true,
        EnableDutyNotifications = true,
        EnableDiscordWebhook = true
    },
}

--------------------------------------------------------------------------------------------------------
-------------------------- POLICE OPTIONS  -------------------------------------------------------------
--------------------------------------------------------------------------------------------------------

Config.jobMenu = 'F9'

Config.customCarlock = false

Config.billingSystem = false

Config.skinScript = true

Config.customJail = true

Config.inventory = 'ox'
Config.searchPlayers = true

Config.weaponsAsItems = true

Config.esxIdentity = false
Config.esxLicense = true

Config.spikeStripsEnabled = true

Config.playerSelection = {
    jail = {
        enabled = true,
        range = 4.0
    },
    search = {
        enabled = true,
        range = 2.0
    }
}

Config.tackle = {
    enabled = true,
    policeOnly = true,
    hotkey = 'G'
}

Config.handcuff = {
    timer = 20 * 60000,
    hotkey = 'J',
    item = 'handcuffs',
    requireItem = true,
    requireCompliance = true,
    taserWindow = 10000,
    skilledEscape = {
        enabled = false,
        difficulty = {'easy', 'easy', 'easy'}
    }
}

Config.policeJobs = {
    'police'
}

--------------------------------------------------------------------------------------------------------
-------------------------- PROPS CONFIG  ---------------------------------------------------------------
--------------------------------------------------------------------------------------------------------

Config.Props = {

    {
        title = 'Barrier',
        description = '',
        model = "prop_barrier_work05",
        groups = {
            ['police'] = 0,
            ['sheriff'] = 0,
        }
    },
    {
        title = 'Barricade',
        description = '',
        model = "prop_mp_barrier_01",
        groups = {
            ['police'] = 0,
            ['sheriff'] = 0,
        }
    },
    {
        title = 'Traffic Cones',
        description = '',
        model = "prop_roadcone02a",
        groups = {
            ['police'] = 0,
            ['sheriff'] = 0,
        }
    },
    {
        title = 'Spike Strip',
        description = '',
        model = "p_ld_stinger_s",
        groups = {
            ['police'] = 0,
            ['sheriff'] = 0,
        }
    },

}

--------------------------------------------------------------------------------------------------------
-------------------------- BLIPS AND MENUS CONFIG  -----------------------------------------------------
--------------------------------------------------------------------------------------------------------


Config.Locations = {
    LSPD = {
        blip = {
            enabled = true,
            coords = vector3(102.54, -389.44, 48.52),
            sprite = 60,
            color = 29,
            scale = 1.0,
            string = 'Mission Row PD'
        },

        bossMenu = {
            enabled = true,
            jobLock = 'police',
            coords = vec3(0,0,0),
            label = '[E] - Access Boss Menu',
            distance = 3.0,
            target = {
                enabled = true,
                label = 'Access Boss Menu',
                coords = vector3(87.4, -361.16, 53.12),
                heading = 70,
                width = 0.2,
                length = 0.6,
                minZ = 50.27,
                maxZ = 54.27
            }
        },

        armoury = {
            enabled = false,
            coords = vec3(454.0439, -980.055, 29.689),
            heading = 86.95,
            ped = 's_f_y_cop_01',
            label = '[E] - Access Armoury',
            jobLock = 'police',
            weapons = {
                [0] = {
                ['WEAPON_COMBATPISTOL'] = { label = 'Combat Pistol', multiple = false, price = 0 },
                ['WEAPON_NIGHTSTICK'] = { label = 'Night Stick', multiple = false, price = 0 },
                ['WEAPON_STUNGUN'] = { label = 'Taser', multiple = false, price = 0 },
                ['ammo-9'] = { label = '9mm Ammo', multiple = true, price = 0 },
                ['armour'] = { label = 'Bulletproof Vest', multiple = false, price = 0 },

                },
                [1] = {
                    ['WEAPON_COMBATPISTOL'] = { label = 'Combat Pistol', multiple = false, price = 0 },
                    ['WEAPON_NIGHTSTICK'] = { label = 'Night Stick', multiple = false, price = 0 },
                    ['WEAPON_STUNGUN'] = { label = 'Taser', multiple = false, price = 0 },
                    ['ammo-9'] = { label = '9mm Ammo', multiple = true, price = 0 },
                    ['armour'] = { label = 'Bulletproof Vest', multiple = false, price = 0 },
                },
                [2] = {
                    ['WEAPON_COMBATPISTOL'] = { label = 'Combat Pistol', multiple = false, price = 0 },
                    ['WEAPON_NIGHTSTICK'] = { label = 'Night Stick', multiple = false, price = 0 },
                    ['WEAPON_STUNGUN'] = { label = 'Taser', multiple = false, price = 0 },
                    ['ammo-9'] = { label = '9mm Ammo', multiple = true, price = 0 },
                    ['armour'] = { label = 'Bulletproof Vest', multiple = false, price = 0 },
                },
                [3] = {
                    ['WEAPON_COMBATPISTOL'] = { label = 'Combat Pistol', multiple = false, price = 0 },
                    ['WEAPON_NIGHTSTICK'] = { label = 'Night Stick', multiple = false, price = 0 },
                    ['WEAPON_STUNGUN'] = { label = 'Taser', multiple = false, price = 0 },
                    ['ammo-9'] = { label = '9mm Ammo', multiple = true, price = 0 },
                    ['armour'] = { label = 'Bulletproof Vest', multiple = false, price = 0 },
                },
            }
        },

        cloakroom = {
            enabled = false,
            jobLock = 'police',
            coords = vec3(452.2905, -992.885, 30.689),
            label = '[E] - Cloak Room',
            range = 2.0,
            uniforms = {

                [1] = {
                    label = 'Patrol',
                    male = {
                        ['tshirt_1'] = 58,  ['tshirt_2'] = 0,
                        ['torso_1'] = 55,   ['torso_2'] = 0,
                        ['arms'] = 30,
                        ['pants_1'] = 24,   ['pants_2'] = 0,
                        ['shoes_1'] = 10,   ['shoes_2'] = 0,
                        ['helmet_1'] = 3,  ['helmet_2'] = 0,
                        ['bproof_1'] = 1,   ['bproof_2'] = 2,
                    },
                    female = {
                        ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
                        ['torso_1'] = 4,   ['torso_2'] = 14,
                        ['arms'] = 4,
                        ['pants_1'] = 25,   ['pants_2'] = 1,
                        ['shoes_1'] = 16,   ['shoes_2'] = 4,
                        ['bproof_1'] = 1,   ['bproof_2'] = 2,
                    }
                },

                -- [2] = {
                --     label = 'Chief',
                --     male = {
                --         ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
                --         ['torso_1'] = 5,   ['torso_2'] = 2,
                --         ['arms'] = 5,
                --         ['pants_1'] = 6,   ['pants_2'] = 1,
                --         ['shoes_1'] = 16,   ['shoes_2'] = 7,
                --         ['helmet_1'] = 44,  ['helmet_2'] = 7,
                --     },
                --     female = {
                --         ['tshirt_1'] = 15,  ['tshirt_2'] = 0,
                --         ['torso_1'] = 4,   ['torso_2'] = 14,
                --         ['arms'] = 4,
                --         ['pants_1'] = 25,   ['pants_2'] = 1,
                --         ['shoes_1'] = 16,   ['shoes_2'] = 4,
                --     }
                -- },

            }

        },

--------------------------------------------------------------------------------------------------------
-------------------------- GARAGE CONFIG  --------------------------------------------------------------
--------------------------------------------------------------------------------------------------------


        vehicles = {
            enabled = false,
            jobLock = 'police',
            zone = {
                coords = vec3(463.69, -1019.72, 28.1),
                range = 5.5,
                label = '[E] - Access Garage',
                return_label = '[E] - Return Vehicle'
            },
            spawn = {
                land = {
                    coords = vec3(449.37, -1025.46, 28.59),
                    heading = 3.68
                },
                air = {
                    coords = vec3(449.29, -981.76, 43.69),
                    heading =  0.01
                }
            },
            options = {

                [0] = {
                    ['police'] = {
                        label = 'Police Cruiser',
                        category = 'land',
                    },
                    ['police2'] = {
                        label = 'Police Cruiser #2',
                        category = 'land',
                    },
                    ['polmav'] = {
                        label = 'Maverick',
                        category = 'air',
                    },
                },

                [1] = {
                    ['police'] = {
                        label = 'Police Cruiser',
                        category = 'land',
                    },
                    ['police2'] = {
                        label = 'Police Cruiser #2',
                        category = 'land',
                    },
                    ['polmav'] = {
                        label = 'Maverick',
                        category = 'air',
                    },
                },

                [2] = {
                    ['police'] = {
                        label = 'Police Cruiser',
                        category = 'land',
                    },
                    ['police2'] = {
                        label = 'Police Cruiser #2',
                        category = 'land',
                    },
                    ['polmav'] = {
                        label = 'Maverick',
                        category = 'air',
                    },
                },

                [3] = {
                    ['police'] = {
                        label = 'Police Cruiser',
                        category = 'land',
                    },
                    ['police2'] = {
                        label = 'Police Cruiser #2',
                        category = 'land',
                    },
                    ['polmav'] = {
                        label = 'Maverick',
                        category = 'air',
                    },
                },

            }
        }

    },

}

--------------------------------------------------------------------------------------------------------
-------------------------- EMERGENCY BLIP CONFIG  ------------------------------------------------
--------------------------------------------------------------------------------------------------------

Config.PlayerBlips = {
    ENABLE = true,

    update_timer = 1,
    flashing_blips = true,
    minimize_longdistance = false,

    allowed_jobs = {'police', 'ambulance'},

    blip_sprites = {
        ['foot'] = 1,
        ['car'] = 225,
        ['motorcycle'] = 226,
        ['helicopter'] = 43,
        ['boat'] = 427,
    },

    blip_colours = {
        ['police'] = {
            default = 3,
            emergency = 1
        },
        ['ambulance'] = {
            default = 1,
            emergency = 6
        }
    }
}