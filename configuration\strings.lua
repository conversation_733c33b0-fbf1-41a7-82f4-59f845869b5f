Strings = {

    cloakroom = 'Cloakroom',
    civilian_wear = 'Civilian Wear',
    police_garage = 'Police Garage',
    armoury_menu = 'Police Armoury',
    quantity = 'Quantity',
    currency = '$',
    police = 'Police',
    success = 'Success',

    minutes_dialog = 'Minutes to Jail',
    minutes_dialog_field = 'Minutes',
    armoury_quantity_dialog = 'How Many',

    no_nearby = 'No One Nearby',
    no_nearby_desc = 'There is no one nearby.',
    invalid_amount = 'Invalid Amount',
    invalid_amount_desc = 'Invalid amount entered.',
    successful_purchase_desc = 'You purchased the item.',
    lacking_funds = 'Lacking Funds',
    lacking_funds_desc = 'You do not have enough funds for this purchase.',
    not_restrained = 'Not Restrained',
    not_restrained_desc = 'This person is not restrained.',
    unconcious = 'Unconcious',
    unconcious_desc = 'This person is unconscious.',
    license_revoked = 'License Revoked',
    license_revoked_desc = 'You have successfully revoked the citizen\'s license.',
    too_far = 'Too Far',
    too_far_desc = 'You are too far from this vehicle.',
    vehicle_not_found = 'Vehicle Not Found',
    vehicle_not_found_desc = 'No vehicle detected nearby.',
    car_impounded_desc = 'You successfully impounded this vehicle.',
    lockpicked = 'Lock Picked',
    lockpicked_desc = 'You successfully picked this vehicle\'s lock.',
    cancelled = 'Cancelled',
    cancelled_desc = 'You cancelled this action.',
    driver_in_car = 'Driver in Vehicle',
    driver_in_car_desc = 'There is a driver in this vehicle.',
    no_permission = 'No Access',
    no_access_desc = 'You do not have access to this.',

    lockpick_progress = 'Lockpicking vehicle...',
    impounding_progress = 'Impounding vehicle...',

    go_back = 'Go Back',
    licenses = 'Licenses',
    id_result_menu = 'Identification Info',
    no_licenses = 'No licenses found.',
    total_licenses = 'Total licenses:',

    vehicle_interactions = 'Vehicle Interactions',
    vehicle_information = 'Vehicle Information',
    vehicle_information_desc = 'Display information about a vehicle',
    lockpick_vehicle = 'Lockpick Vehicle',
    locakpick_vehicle_desc = 'Lockpick a vehicle to unlock it',
    impound_vehicle = 'Impound Vehicle',
    impound_vehicle_desc = 'Impound a vehicle to send to the depot',
    plate = 'Plate',
    owner = 'Owner',
    possibly_stolen = 'Possibly Stolen',
    possibly_stolen_desc = 'No owner found in database',

    name = 'Name',
    job = 'Occupation',
    job_position = 'Position',
    dob = 'Date of Birth',
    sex = 'Sex',
    bac = 'BAC Level',
    revoke_license = 'Revoke License',

    key_map_job = 'Open Police Menu',
    key_map_cuff = 'Handcuff / Uncuff',
    key_map_tackle = 'Tackle Person',
    place_object = 'Place Object',
    place_object_desc = 'Place an object in the world',
    check_id = 'Check ID',
    check_id_desc = 'Check ID of a person',
    search_player = 'Search Person',
    search_player_desc = 'Search a nearby person',
    handcuff_player = 'Cuff / Uncuff',
    handcuff_player_desc = 'Handcuff or uncuff a person',
    escort_player = 'Escort Person',
    escort_player_desc = 'Escort a nearby person',
    jail_player = 'Send To Prison',
    jail_player_desc = 'Send a nearby person to prison',
    fines = 'Billing',
    fines_desc = 'Bill a nearby person',
    put_in_vehicle = 'Place in Vehicle',
    put_in_vehicle_desc = 'Place a person in a vehicle',
    take_out_vehicle = 'Remove from Vehicle',
    take_out_vehicle_desc = 'Remove a person from vehicle',

    invalid_entry = 'Invalid Entry',
    invalid_entry_desc = 'You must enter a valid positive number.',

    emergency_lights_on = 'Emergency lights activated',
    emergency_lights_off = 'Emergency lights deactivated',

    need_handcuffs = 'You need handcuffs to restrain someone',
    need_handcuffs_desc = 'Make sure you have handcuffs in your inventory',
    target_in_vehicle = 'Target in Vehicle',
    target_in_vehicle_desc = 'Cannot handcuff/uncuff someone while they are in a vehicle',
    player_in_vehicle = 'You are in Vehicle',
    player_in_vehicle_desc = 'You cannot handcuff/uncuff while you are in a vehicle',
    stop_escorting_first = 'Stop Escorting First',
    stop_escorting_first_desc = 'You must stop escorting before uncuffing the person',
    target_not_compliant = 'Target Not Compliant',
    target_not_compliant_desc = 'Target must have hands up or be recently tasered to be handcuffed',
    target_tasered = 'Target Tasered',
    target_tasered_desc = 'You can now handcuff this person within 10 seconds',
    player_tasered = 'You Were Tasered',
    player_tasered_desc = 'Police can now handcuff you',

    -- Player Selection System
    select_player = 'Select Player',
    select_player_desc = 'Choose a player from the list',
    nearby_players = 'Nearby Players',
    no_players_nearby = 'No Players Nearby',
    no_players_nearby_desc = 'There are no players within range',
    player_distance = 'Distance: %.1fm',
    select_target = 'Select Target',

}